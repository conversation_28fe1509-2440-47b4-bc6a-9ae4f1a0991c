package com.geeksec.certificateanalyzer.sink;

<<<<<<< HEAD:flink-jobs/certificate-analyzer/src/main/java/com/geeksec/certificateanalyzer/output/CertificateOutputManager.java
import org.apache.flink.api.java.utils.ParameterTool;

import com.geeksec.certificateanalyzer.config.CertificateAnalyzerConfig;
import com.geeksec.certificateanalyzer.sink.doris.DorisCertificateSinkManager;
import com.geeksec.certificateanalyzer.pipeline.CertificateProcessingPipeline;
=======
import org.apache.flink.streaming.api.datastream.DataStream;

import com.geeksec.certificateanalyzer.config.CertificateAnalyzerConfig;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.sink.minio.MinioFileSinkFactory;
>>>>>>> add875aa (refactor: 重构证书分析器代码结构，优化模块划分和命名):flink-jobs/certificate-analyzer/src/main/java/com/geeksec/certificateanalyzer/sink/CertificateOutputManager.java

import lombok.extern.slf4j.Slf4j;

/**
 * 证书输出管理器
<<<<<<< HEAD:flink-jobs/certificate-analyzer/src/main/java/com/geeksec/certificateanalyzer/output/CertificateOutputManager.java
 * 负责配置和管理所有输出组件
 *
 * <AUTHOR>
=======
 * 负责管理所有与证书相关的输出
 *
 * <AUTHOR>
 * @date 2024/12/16
>>>>>>> add875aa (refactor: 重构证书分析器代码结构，优化模块划分和命名):flink-jobs/certificate-analyzer/src/main/java/com/geeksec/certificateanalyzer/sink/CertificateOutputManager.java
 */
@Slf4j
public class CertificateOutputManager {

    /**
     * 配置所有输出组件
     *
     * @param pipelineResult 流水线处理结果
     * @param config 配置参数
     */
    public static void configureAllOutputs(CertificateProcessingPipeline.PipelineResult pipelineResult,
                                         ParameterTool config) {
        log.info("配置所有输出组件");

        // 配置Doris输出（如果启用）
        if (CertificateAnalyzerConfig.isDorisEnabled()) {
            configureDorisOutput(pipelineResult, config);
        }

        // 配置Nebula输出（如果启用）
        if (CertificateAnalyzerConfig.isNebulaEnabled()) {
            configureNebulaOutput(pipelineResult, config);
        }

        // 配置PostgreSQL输出（如果启用）
        if (CertificateAnalyzerConfig.isPostgreSQLEnabled()) {
            configurePostgreSQLOutput(pipelineResult, config);
        }

        log.info("所有输出组件配置完成");
    }

    /**
     * 配置Doris数据仓库输出
     *
     * @param pipelineResult 流水线处理结果
     * @param config 配置参数
     */
    private static void configureDorisOutput(CertificateProcessingPipeline.PipelineResult pipelineResult,
                                           ParameterTool config) {
        log.info("配置Doris数据仓库输出");

        if (pipelineResult.getProcessedStream() != null) {
            // 添加证书Doris输出
            DorisCertificateSinkManager.addDorisCertificateOutput(
                    pipelineResult.getProcessedStream(), config);

            // 添加威胁证书Doris输出（可选）
            DorisSinkManager.addThreatCertificateDorisSink(
                    pipelineResult.getProcessedStream(), config);

            log.info("Doris输出配置完成");
        } else {
            log.warn("处理流为空，跳过Doris输出配置");
        }
    }

    /**
     * 配置Nebula图数据库输出
     *
     * @param pipelineResult 流水线处理结果
     * @param config 配置参数
     */
    private static void configureNebulaOutput(CertificateProcessingPipeline.PipelineResult pipelineResult,
                                            ParameterTool config) {
        log.info("配置Nebula图数据库输出");

        if (pipelineResult.getProcessedStream() != null) {
            // 暂时注释掉Nebula输出，等待实现完成
            log.info("Nebula输出功能待实现");
            // pipelineResult.getProcessedStream()
            //         .addSink(new CertificateNebulaSink())
            //         .name("证书Nebula图数据库输出")
            //         .setParallelism(2);
        }
    }

    /**
     * 配置PostgreSQL输出
     *
     * @param pipelineResult 流水线处理结果
     * @param config 配置参数
     */
    private static void configurePostgreSQLOutput(CertificateProcessingPipeline.PipelineResult pipelineResult,
                                                ParameterTool config) {
        log.info("配置PostgreSQL输出");

        if (pipelineResult.getProcessedStream() != null) {
            // 暂时注释掉PostgreSQL输出，等待实现完成
            log.info("PostgreSQL输出功能待实现");
            // pipelineResult.getProcessedStream().addSink(new PostgreSQLCertSink());
        }
    }


=======
     * 添加证书相关的输出
     *
     * @param certificateStream 证书数据流
     */
    public static void addCertificateOutputs(DataStream<X509Certificate> certificateStream) {
        log.info("开始添加证书相关输出");

        // 证书存储输出
        certificateStream.addSink(MinioFileSinkFactory.createCertificateSink())
                .name("证书MinIO存储")
                .setParallelism(CertificateAnalyzerConfig.getAnalyzerParallelism());
        log.info("已添加证书MinIO存储输出");

        // 证书关系图输出（Nebula）
        if (CertificateAnalyzerConfig.isNebulaEnabled()) {
            certificateStream.addSink(new com.geeksec.certificateanalyzer.output.nebula.CertNebulaSinkFunction())
                    .name("证书关系图")
                    .setParallelism(CertificateAnalyzerConfig.getNebulaParallelism());
            log.info("已添加证书关系图输出");
        }

        // 证书缓存输出（Redis）
        if (CertificateAnalyzerConfig.isRedisEnabled()) {
            certificateStream.addSink(new com.geeksec.certificateanalyzer.output.redis.RedisCertificateSink())
                    .name("证书Redis缓存")
                    .setParallelism(CertificateAnalyzerConfig.getRedisParallelism());
            log.info("已添加证书Redis缓存输出");
        }

        log.info("证书相关输出添加完成");
    }
>>>>>>> add875aa (refactor: 重构证书分析器代码结构，优化模块划分和命名):flink-jobs/certificate-analyzer/src/main/java/com/geeksec/certificateanalyzer/sink/CertificateOutputManager.java
}
