package com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
<<<<<<< HEAD:flink-jobs/certificate-analyzer/src/main/java/com/geeksec/certificateanalyzer/pipeline/detection/detector/impl/BlockedCertificateDetector.java
import com.geeksec.certificateanalyzer.pipeline.detection.detector.BaseCertificateDetector;
import com.geeksec.common.infrastructure.database.redis.RedisConnectionManager;
=======
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.BaseCertificateDetector;
import com.geeksec.common.utils.db.redis.RedisConnectionManager;
>>>>>>> add875aa (refactor: 重构证书分析器代码结构，优化模块划分和命名):flink-jobs/certificate-analyzer/src/main/java/com/geeksec/certificateanalyzer/operator/analysis/detection/detector/impl/BlockedCertificateDetector.java
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.Set;

/**
 * 被阻止证书检测器
 * 负责检查证书是否在阻止列表中
 */
@Slf4j
public class BlockedCertificateDetector extends BaseCertificateDetector {
    private static JedisPool jedisPool;

    static {
        // 初始化Redis连接池
        jedisPool = RedisConnectionManager.initJedisPool();
        log.info("Initialized Redis connection pool");
    }

    public BlockedCertificateDetector() {
        super("Blocked Certificate Detector");
    }

    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        String sha1 = cert.getCorrectedAsn1Sha1();
        if (sha1 == null || sha1.isEmpty()) {
            sha1 = cert.getDerSha1();
        }

        if (sha1 == null || sha1.isEmpty()) {
            return;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            // 检查证书是否在阻止列表中
            if (isBlocked(jedis, sha1)) {
                Set<CertificateLabel> labels = cert.getLabels();
                labels.add(CertificateLabel.BLOCKED);
                log.debug("Detected blocked certificate: {}", sha1);
            }
        } catch (Exception e) {
            log.error("Error checking certificate blacklist", e);
        }
    }

    private boolean isBlocked(Jedis jedis, String sha1) {
        // 实现阻止列表检查逻辑
        // 这里可以添加Redis查询逻辑
        return false;
    }
}
